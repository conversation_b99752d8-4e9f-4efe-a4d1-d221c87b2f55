

import asyncdispatch, asynchttpserver, ws, json
import utils/log
import plugin_manager
import dispatcher

type
  BotServer* = ref object
    server: AsyncHttpServer
    port: int

proc newBotServer*(port: int = 33894): BotServer =
  BotServer(server: newAsyncHttpServer(), port: port)

proc handleMessage(ws: WebSocket, msg: string) {.async, gcsafe.} =
  logInfo("收到消息: " & msg)
  try:
    let event = parseJson(msg)
    let disp = newDispatcher()
    disp.handle(event, ws)
  except:
    logError("消息处理失败: " & getCurrentExceptionMsg())

proc onConnect(ws: WebSocket) {.async, gcsafe.} =
  logInfo("客户端已连接")
  try:
    while ws.readyState == Open:
      let msg = await ws.receiveStrPacket()
      await handleMessage(ws, msg)
  except WebSocketError as e:
    logError("WebSocket错误: " & e.msg)
  except Exception as e:
    logError("连接异常: " & e.msg)
  finally:
    logInfo("客户端连接已断开")

proc start*(bot: BotServer) {.async.} =
  proc cb(req: Request) {.async, gcsafe.} =
    logInfo("收到HTTP请求: " & req.url.path & " 来自: " & req.hostname)
    if req.url.path == "/":
      try:
        let ws = await newWebSocket(req)
        logInfo("WebSocket握手成功")
        await onConnect(ws)
      except WebSocketError as e:
        logError("WebSocket握手失败: " & e.msg)
        await req.respond(Http400, "WebSocket握手失败")
    else:
      await req.respond(Http404, "Not Found")
  
  try:
    logInfo("正在启动服务器...")
    logInfo("服务器启动成功，端口: " & $bot.port)
    logInfo("开始监听连接...")
    await bot.server.serve(Port(bot.port), cb)
  except Exception as e:
    logError("服务器启动失败: " & e.msg)
    logError("错误详情: " & e.getStackTrace())
