import json, strutils, sequtils, re

type
  RuleFunc* = proc(event: JsonNode): bool {.gcsafe.}
  HandlerFunc* = proc(bot: Bot, event: JsonNode) {.gcsafe.}
  
  MatcherOptions* = object
    priority*: int
    `block`*: bool
    temp*: bool
    forceWhitespace*: Option[bool]
    splitBy*: string
    aliases*: seq[string]
  
  Matcher* = ref object
    eventType*: string
    rule*: RuleFunc
    handler*: HandlerFunc
    priority*: int
    `block`*: bool
    temp*: bool

var matchers: seq[Matcher] = @[]

proc splitArgs(args: string, separator = ""): seq[string] =
  if args.len == 0: return @[]
  
  if separator == ",":
    args.split(',').mapIt(it.strip())
  else:
    args.splitWhitespace()

proc newMatcher*(eventType: string, rule: RuleFunc, handler: HandlerFunc, options: MatcherOptions = MatcherOptions()): Matcher =
  let m = Matcher(
    eventType: eventType,
    rule: rule,
    handler: handler,
    priority: options.priority.get(1),
    `block`: options.`block`,
    temp: options.temp
  )
  matchers.add(m)
  m

proc on*(eventType: string, rule: RuleFunc, handler: HandlerFunc, options: MatcherOptions = MatcherOptions()): Matcher =
  newMatcher(eventType, rule, handler, options)

proc onMessage*(rule: RuleFunc, handler: HandlerFunc, options: MatcherOptions = MatcherOptions()): Matcher =
  var opts = options
  if not opts.`block`.isSome: opts.`block` = true
  on("message", rule, handler, opts)

proc onCommand*(cmd: string, handler: HandlerFunc, options: MatcherOptions = MatcherOptions()): Matcher =
  let commands = @[cmd] & options.aliases
  
  let rule = proc(event: JsonNode): bool =
    let msg = event{"raw_message"}.getStr("")
    if msg.len == 0: return false
    
    for command in commands:
      let pattern = case options.forceWhitespace:
        of some(true): "^" & command & r"\s"
        of some(false): "^" & command
        else: "^" & command & r"(\s|$)"
      
      if msg.match(re(pattern)): return true
    false
  
  let wrappedHandler = proc(bot: Bot, event: JsonNode) =
    let msg = event{"raw_message"}.getStr("")
    var matchedCmd = ""
    var args = ""
    
    for command in commands:
      let pattern = case options.forceWhitespace:
        of some(true): "^" & command & r"\s+(.*)"
        of some(false): "^" & command & r"\s*(.*)"
        else: "^" & command & r"\s+(.*)"
      
      let matches = msg.findAll(re(pattern))
      if matches.len > 0:
        matchedCmd = command
        args = if matches.len > 1: matches[1] else: ""
        break
    
    var mutableEvent = event
    mutableEvent["args"] = %args
    mutableEvent["matched_command"] = %matchedCmd
    
    if options.splitBy.len > 0:
      mutableEvent["args_list"] = %splitArgs(args, options.splitBy)
    
    handler(bot, mutableEvent)
  
  onMessage(rule, wrappedHandler, options)

proc onKeyword*(keywords: seq[string], handler: HandlerFunc, options: MatcherOptions = MatcherOptions()): Matcher =
  let rule = proc(event: JsonNode): bool =
    let msg = event{"raw_message"}.getStr("")
    keywords.anyIt(msg.contains(it))
  
  onMessage(rule, handler, options)

proc getMatchers*(): seq[Matcher] = matchers